<template>
  <div class="app-container">

    <el-row :gutter="20">
      <!--分类数据-->
      <el-col :span="4" :xs="24" style="max-height: 86vh;overflow-y: auto;">
        <div class="head-container">
          <el-input v-model="typeName" placeholder="请输入分类名称" clearable size="small" prefix-icon="el-icon-search"
            style="margin-bottom: 20px" />
        </div>
        <div class="head-container">
          <el-tree :data="typeOptions" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" :highlight-current="true" ref="tree" default-expand-all
            @node-click="handleNodeClick" />
        </div>
      </el-col>
      <!--文章数据-->
      <el-col :span="20" :xs="24">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="中文文章" name="first"></el-tab-pane>
          <el-tab-pane label="其他语言文章" name="second"></el-tab-pane>
        </el-tabs>
        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="文字标题" prop="title">
            <el-input v-model="queryParams.title" placeholder="请输入文字标题" clearable size="small"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item v-if="showLanguageType" label="语言类型" prop="languageType">
            <el-select v-model="queryParams.languageType" placeholder="请选择语言类型" clearable size="small">
              <el-option v-for="dict in dict.type.i18N_language" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <!--      <el-form-item label="所属分类名称" prop="articleTypeName">-->
          <!--        <el-input-->
          <!--          v-model="queryParams.articleTypeName"-->
          <!--          placeholder="请输入所属分类名称"-->
          <!--          clearable-->
          <!--          size="small"-->
          <!--          @keyup.enter.native="handleQuery"-->
          <!--        />-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="作者" prop="author">-->
          <!--        <el-input-->
          <!--          v-model="queryParams.author"-->
          <!--          placeholder="请输入作者"-->
          <!--          clearable-->
          <!--          size="small"-->
          <!--          @keyup.enter.native="handleQuery"-->
          <!--        />-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="来源" prop="originFrom">-->
          <!--        <el-input-->
          <!--          v-model="queryParams.originFrom"-->
          <!--          placeholder="请输入来源"-->
          <!--          clearable-->
          <!--          size="small"-->
          <!--          @keyup.enter.native="handleQuery"-->
          <!--        />-->
          <!--      </el-form-item>-->
          <el-form-item label="发布日期">
            <el-date-picker v-model="daterangePublicDate" size="small" style="width: 240px" value-format="yyyy-MM-dd"
              type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
              v-hasPermi="['yq:article:add']">新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
              v-hasPermi="['yq:article:edit']">修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
              v-hasPermi="['yq:article:remove']">删除
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="articleList" height="62vh" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="文字标题" align="center" prop="title" />
          <el-table-column label="所属分类名称" align="center" prop="articleTypeName" />
          <el-table-column label="主图" align="center" prop="mainImgUrl" width="100">
            <template slot-scope="scope">
              <image-preview v-if="scope.row.mainImgUrl" :src="scope.row.mainImgUrl" :width="50" :height="50" />
              <img v-else src="@/assets/images/quexin.png" style="width: 80px;height: 80px">
            </template>
          </el-table-column>
          <el-table-column label="作者" align="center" prop="author" />
          <el-table-column label="来源" align="center" prop="originFrom" />
          <el-table-column width="200" label="语言类型" align="center" prop="languageType">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.i18N_language" :value="scope.row.languageType" />
            </template>
          </el-table-column>
          <el-table-column label="发布日期" align="center" prop="publicDate" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.publicDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否已禁用" align="center" prop="status">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.status_yes_no" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="排序" align="center" prop="orderNum" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['yq:article:edit']">修改
              </el-button>
              <!--              <el-button-->
              <!--                size="mini"-->
              <!--                type="text"-->
              <!--                icon="el-icon-edit"-->
              <!--                @click="handleCopy(scope.row)"-->
              <!--                v-hasPermi="['yq:article:edit']"-->
              <!--              >复制-->
              <!--              </el-button>-->
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                v-hasPermi="['yq:article:remove']">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改新闻对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="75%" append-to-body :close-on-click-modal="false">
          <!-- 语言标签页和按钮 -->
          <div style="position: relative; margin-bottom: 20px;">
            <el-tabs v-if="supportedLanguages && supportedLanguages.length > 0" v-model="activeLanguageTab"
              @tab-click="handleLanguageTabClick">
              <el-tab-pane v-for="lang in supportedLanguages" :key="lang.value" :label="lang.label"
                :name="lang.value"></el-tab-pane>
            </el-tabs>

            <div
              style="position: absolute; top: 0; right: 0; margin-top: 3px; background-color: #fff; padding-left: 15px;">
              <el-button size="mini" type="success" icon="el-icon-refresh" :loading="translating"
                @click="handleAutoTranslate">自动翻译其他语言</el-button>
              <el-button size="mini" type="warning" icon="el-icon-delete"
                @click="handleClearOtherLanguages">清空其他语言</el-button>
            </div>
          </div>

          <el-form ref="form" :model="multiLanguageForms[activeLanguageTab]" :rules="rules" label-width="150px"
            v-if="multiLanguageForms[activeLanguageTab]">
            <el-form-item label="文字标题" prop="title">
              <template #label>
                <span>
                  <span>文字标题</span>
                  <span class="label-desc">{{ labelDesc('title') }}</span>
                </span>
              </template>
              <el-input v-model="multiLanguageForms[activeLanguageTab].title" placeholder="请输入文字标题" />
            </el-form-item>
            <el-form-item label="所属分类" prop="articleTypeId">
              <treeselect v-model="multiLanguageForms[activeLanguageTab].articleTypeId" :options="typeOptions"
                :show-count="true" placeholder="请选择归属分类"
                @input="handleFormChange(activeLanguageTab, 'articleTypeId', $event)" />
            </el-form-item>

            <el-form-item label="简介" prop="description">
              <el-input type="textarea" :rows="4" show-word-limit maxlength="3000"
                v-model="multiLanguageForms[activeLanguageTab].description" placeholder="请输入简介" />
            </el-form-item>
            <!-- 附件管理区域 -->
            <el-form-item label="附件管理" v-if="showFileManagement">
              <div class="file-management">
                <!-- 文件上传区域 -->
                <HJUpload v-if="open" :accept-type="'pdf'" :show-file="false"
                  :file-arr.sync="multiLanguageForms[activeLanguageTab].articleFiles" :limit="20"
                  @file-change="handleFileChange">
                </HJUpload>

                <!-- 文件列表 -->
                <el-table :data="multiLanguageForms[activeLanguageTab].articleFiles"
                  style="width: 100%; margin-top: 10px;">
                  <el-table-column label="文件名" prop="fileName" />
                  <el-table-column label="发布日期" width="250">
                    <template slot-scope="scope">
                      <el-date-picker v-model="scope.row.publicDate" type="datetime" placeholder="选择发布日期"
                        value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptions">
                      </el-date-picker>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="100">
                    <template slot-scope="scope">
                      <el-button size="mini" type="text" @click="handleFileDelete(scope.$index)" icon="el-icon-delete">
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-form-item>
            <el-form-item label="作者" prop="author">
              <template #label>
                <span>
                  <span>作者</span>
                  <span class="label-desc">{{ labelDesc('author') }}</span>
                </span>
              </template>
              <el-input v-model="multiLanguageForms[activeLanguageTab].author" placeholder="请输入作者" />
            </el-form-item>
            <el-form-item label="来源" prop="originFrom">
              <template #label>
                <span>
                  <span>来源</span>
                  <span class="label-desc">{{ labelDesc('originFrom') }}</span>
                </span>
              </template>
              <el-input v-model="multiLanguageForms[activeLanguageTab].originFrom" placeholder="请输入来源" />
            </el-form-item>
            <el-form-item label="发布日期" prop="publicDate">
              <el-date-picker clearable size="small" v-model="multiLanguageForms[activeLanguageTab].publicDate"
                type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择发布日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="视频">
              <b-upload v-if="open" acceptType="video"
                :video-url.sync="multiLanguageForms[activeLanguageTab].videoUrl"></b-upload>
              <!--              <file-upload v-model="form.videoUrl"/>-->
            </el-form-item>
            <el-form-item label="主图" prop="mainImgUrl">
              <b-img-draggable v-if="open" :img-url.sync="multiLanguageForms[activeLanguageTab].mainImgUrl"
                :limit="1"></b-img-draggable>
            </el-form-item>
            <el-form-item label="外部链接" prop="grainWeight">
              <el-input v-model="multiLanguageForms[activeLanguageTab].externalLinks" placeholder="请输入外部链接" />
            </el-form-item>
            <el-form-item label="文件" prop="attachment">
              <HJUpload v-if="open" :file-arr.sync="multiLanguageForms[activeLanguageTab].attachment" :limit="1">
              </HJUpload>
            </el-form-item>
            <el-form-item label="是否已禁用">
              <el-radio-group v-model="multiLanguageForms[activeLanguageTab].status">
                <el-radio v-for="dict in dict.type.status_yes_no" :key="dict.value" :label="dict.value">{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="详情内容">
              <Tinymce v-if="open" ref="editor" v-model="multiLanguageForms[activeLanguageTab].content" :height="400" />
            </el-form-item>
            <el-form-item label="排序" prop="orderNum">
              <el-input-number v-model="multiLanguageForms[activeLanguageTab].orderNum" :min="0" :max="999999"
                :precision="0" placeholder="请输入排序" />
            </el-form-item>


          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { listArticle, getArticle, delArticle, addArticle, updateArticle, autoTranslateArticleData, batchSaveMultiLanguage, getByRelationGroup } from '@/api/yq/article'
import { treeselect } from '@/api/yq/articleType'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import HJUpload from '@/components/YqUpload/HJUpload'

export default {
  name: 'Article',
  dicts: ['status_yes_no', 'i18N_language'],
  components: { HJUpload, Treeselect },
  data() {
    return {
      activeName: 'first',
      showLanguageType: false,
      // 分类名称
      typeName: undefined,
      // 分类树选项
      typeOptions: undefined,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 新闻表格数据
      articleList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangePublicDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        typeCode: 1,
        title: null,
        articleTypeId: null,
        articleTypePath: null,
        articleTypeName: null,
        videoUrl: null,
        mainImgUrl: null,
        description: null,
        author: null,
        originFrom: null,
        publicDate: null,
        content: null,
        status: null,
        orderNum: null,
        orderByColumn: 'orderNum',
        languageType: null,
        isAsc: 'desc'
      },
      // 多语言表单数据
      multiLanguageForms: {},
      // 当前活跃的语言标签页
      activeLanguageTab: 'zh_CN',
      // 翻译状态
      translating: false,
      // 提交状态
      submitting: false,
      // 当前编辑的关系组ID
      currentRelationGroupId: null,
      // 是否已经翻译过(用于判断是否需要二次确认)
      hasTranslated: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: '文字标题不能为空', trigger: 'blur' }
        ],
        articleTypeId: [
          { required: true, message: '所属分类id不能为空', trigger: 'change' }
        ],
        description: [
          { required: true, message: '简介不能为空', trigger: 'blur' }
        ],
        publicDate: [
          { required: true, message: '发布日期不能为空', trigger: 'blur' }
        ],
        languageType: [
          { required: true, message: '语言类型 不能为空', trigger: 'change' }
        ],
        articleFiles: {
          type: 'array',
          validator: (rule, value, callback) => {
            if (this.showFileManagement && (!value || value.length === 0)) {
              callback(new Error('请上传至少一个附件'));
            } else if (this.showFileManagement) {
              const hasEmptyDate = value.some(file => !file.publicDate);
              if (hasEmptyDate) {
                callback(new Error('请为所有附件选择发布日期'));
              } else {
                callback();
              }
            } else {
              callback();
            }
          }
        }
      },
      // 文件管理相关
      showFileManagement: false,
      fileManagementTypeIds: [10014, 10015, 10031, 10032, 15107, 15108, 10012, 10013, 10029, 10030, 15105, 15106], // 需要显示文件管理的文章类型ID
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      }
    }
  },
  computed: {
    // 从字典中获取支持的语言列表
    supportedLanguages() {
      return this.dict.type.i18N_language || []
    }
  },
  watch: {
    // 根据名称筛选部门树
    typeName(val) {
      this.$refs.tree.filter(val)
    },
    'multiLanguageForms': {
      deep: true,
      handler(newVal) {
        if (this.activeLanguageTab && newVal[this.activeLanguageTab]) {
          const shouldShowFileManagement = this.fileManagementTypeIds.includes(newVal[this.activeLanguageTab].articleTypeId);
          // 只有当状态发生变化时才执行清空操作
          if (this.showFileManagement !== shouldShowFileManagement) {
            this.showFileManagement = shouldShowFileManagement;
            if (!shouldShowFileManagement && newVal[this.activeLanguageTab].articleFiles
              && newVal[this.activeLanguageTab].articleFiles.length > 0) {
              // 使用nextTick延迟执行，避免在当前更新循环中触发新的更新
              this.$nextTick(() => {
                this.$set(this.multiLanguageForms[this.activeLanguageTab], 'articleFiles', []);
              });
            }
          }
        }
      }
    }
  },
  created() {
    this.getTreeselect()
    this.queryParams.languageType = 'zh_CN'
    this.getList()
  },
  mounted() {
    // 等待字典数据加载完成后再初始化
    this.$nextTick(() => {
      this.waitForDictAndInitialize()
    })
  },
  methods: {
    // 等待字典数据加载并初始化
    waitForDictAndInitialize() {
      // 检查字典数据是否已加载
      if (this.dict && this.dict.type && this.dict.type.i18N_language && this.dict.type.status_yes_no) {
        this.initializeDefaultLanguage()
      } else {
        // 如果字典数据还未加载，延迟重试
        setTimeout(() => {
          this.waitForDictAndInitialize()
        }, 50)
      }
    },
    // 初始化默认语言
    initializeDefaultLanguage() {
      const languages = this.dict.type.i18N_language
      // 优先选择中文，如果没有则选择第一个
      const zhCN = languages.find(lang => lang.value === 'zh_CN')
      this.activeLanguageTab = zhCN ? 'zh_CN' : languages[0].value
      this.initializeMultiLanguageForms()
    },
    // 初始化多语言表单
    initializeMultiLanguageForms() {
      const languages = this.dict.type.i18N_language
      languages.forEach(lang => {
        if (!this.multiLanguageForms[lang.value]) {
          this.$set(this.multiLanguageForms, lang.value, {
            id: null,
            title: null,
            articleTypeId: null,
            articleTypePath: null,
            typeCode: 1,
            articleTypeName: null,
            videoUrl: null,
            mainImgUrl: null,
            description: null,
            author: null,
            originFrom: null,
            publicDate: null,
            content: null,
            status: '0',
            orderNum: null,
            createBy: null,
            createTime: null,
            updateTime: null,
            updateBy: null,
            delFlag: null,
            languageType: lang.value,
            remark: null,
            fileUrl: null,
            fileName: null,
            externalLinks: null,
            attachment: null,
            articleFiles: []
          })
        }
      })
    },
    handleClick(tab, event) {
      if (tab.index == 0) {
        if (this.showLanguageType) {
          this.queryParams.title = ''
        }
        this.showLanguageType = false
        this.queryParams.languageType = 'zh_CN'
      } else {
        if (!this.showLanguageType) {
          this.queryParams.title = ''
        }
        this.showLanguageType = true
        this.queryParams.languageType = 'en_US'
      }
      this.getList()
    },
    // 语言标签页切换处理
    handleLanguageTabClick(tab) {
      this.activeLanguageTab = tab.name
    },
    // 表单变化处理
    handleFormChange(lang, field, value) {
      // 这里不再同步articleTypeId，使各语言版本可以选择独立的分类
      // 如需同步其他字段，可在此添加
    },
    /** 查询分类下拉树结构 */
    getTreeselect() {
      treeselect({ typeCode: 1 }).then(response => {
        this.typeOptions = this.processTreeOptions(response.data)
      })
    },
    /** 处理树形选项，禁用指定的选项 */
    processTreeOptions(options) {
      const disabledIds = [10012, 15105, 10029] // 需要禁用的选项ID

      const processNode = (node) => {
        // 如果当前节点的ID在禁用列表中，设置为禁用
        if (disabledIds.includes(node.id)) {
          node.isDisabled = true
        }

        // 递归处理子节点
        if (node.children && node.children.length > 0) {
          node.children = node.children.map(child => processNode(child))
        }

        return node
      }

      return options.map(option => processNode(option))
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.articleTypeId = data.id
      this.handleQuery()
    },
    /** 查询新闻列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangePublicDate && '' != this.daterangePublicDate) {
        this.queryParams.params['beginPublicDate'] = this.daterangePublicDate[0]
        this.queryParams.params['endPublicDate'] = this.daterangePublicDate[1]
      }
      this.queryParams.typeCode = 1
      listArticle(this.queryParams).then(response => {
        this.articleList = response.data.rows
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      // 重置多语言表单数据
      this.multiLanguageForms = {}
      this.currentRelationGroupId = null
      // 重置翻译状态
      this.hasTranslated = false
      // 确保字典数据可用后再初始化
      if (this.dict && this.dict.type && this.dict.type.i18N_language) {
        this.initializeMultiLanguageForms()
        this.initializeDefaultLanguage()
      }
      this.showFileManagement = false

      // 重置所有表单验证
      if (this.supportedLanguages && this.supportedLanguages.length > 0) {
        this.$nextTick(() => {
          if (this.$refs.form) {
            this.$refs.form.clearValidate()
          }
        })
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.articleTypeId = null
      this.daterangePublicDate = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加新闻'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getArticle(id).then(response => {
        const currentData = response.data

        // 检查是否有多语言关联关系
        if (currentData.relationGroupId) {
          this.currentRelationGroupId = currentData.relationGroupId
          // 获取所有语言版本
          this.loadMultiLanguageData(currentData.relationGroupId)
        } else {
          // 没有关联关系，只加载当前语言版本
          this.multiLanguageForms[currentData.languageType] = { ...currentData }
          this.activeLanguageTab = currentData.languageType
        }

        this.open = true
        this.title = '修改新闻'
      })
    },
    /** 复制按钮操作 */
    handleCopy(row) {
      this.reset()
      const id = row.id || this.ids
      getArticle(id).then(response => {
        const sourceData = response.data
        // 复制到中文版本，清除ID
        this.multiLanguageForms['zh_CN'] = {
          ...sourceData,
          id: null,
          languageType: 'zh_CN'
        }
        this.activeLanguageTab = 'zh_CN'
        this.open = true
        this.title = '复制新闻'
      })
    },
    /** 加载多语言数据 */
    loadMultiLanguageData(relationGroupId) {
      getByRelationGroup(relationGroupId).then(response => {
        const multiLanguageData = response.data || []
        // 清空现有数据
        this.multiLanguageForms = {}
        this.initializeMultiLanguageForms()

        // 填充已有的多语言数据
        multiLanguageData.forEach(item => {
          if (this.multiLanguageForms[item.languageType]) {
            this.multiLanguageForms[item.languageType] = { ...item }
          }
        })

        // 设置默认激活的tab为中文
        this.activeLanguageTab = 'zh_CN'
      }).catch(error => {
        this.$modal.msgError('加载多语言数据失败：' + (error.msg || error.message))
      })
    },
    /** 自动翻译 */
    handleAutoTranslate() {
      const sourceForm = this.multiLanguageForms[this.activeLanguageTab]
      if (!sourceForm || !sourceForm.title) {
        this.$modal.msgError('请先填写当前语言版本的标题')
        return
      }

      // 判断是否需要二次确认
      if (this.hasTranslated) {
        this.$modal.confirm('再次翻译将覆盖已有的翻译内容，是否确认继续？').then(() => {
          this.executeTranslation(sourceForm);
        }).catch(() => { });
      } else {
        // 首次翻译，直接执行
        this.executeTranslation(sourceForm);
      }
    },

    // 执行翻译的实际逻辑
    executeTranslation(sourceForm) {
      this.translating = true
      autoTranslateArticleData(sourceForm).then(response => {
        const translatedData = response.data || {}
        // 更新其他语言版本的翻译内容
        Object.keys(translatedData).forEach(lang => {
          if (lang !== this.activeLanguageTab) {
            // 确保目标语言的表单对象存在
            if (!this.multiLanguageForms[lang]) {
              const template = { ...sourceForm }
              template.id = null
              template.languageType = lang
              this.$set(this.multiLanguageForms, lang, template)
            }

            // 保留原有的ID和其他系统字段，只更新翻译字段
            this.multiLanguageForms[lang] = {
              ...this.multiLanguageForms[lang],
              ...translatedData[lang],
              languageType: lang
            }
          }
        })

        // 标记已经翻译过
        this.hasTranslated = true
        this.$modal.msgSuccess('翻译完成')
      }).catch(error => {
        this.$modal.msgError('翻译失败：' + (error.msg || error.message))
      }).finally(() => {
        this.translating = false
      })
    },
    /** 清空其他语言版本 */
    handleClearOtherLanguages() {
      this.$modal.confirm('确认清空其他语言版本的数据吗？').then(() => {
        if (this.supportedLanguages && this.supportedLanguages.length > 0) {
          this.supportedLanguages.forEach(lang => {
            if (lang.value !== this.activeLanguageTab) {
              // 使用当前语言的基本模板，但清空内容
              const template = {
                id: null,
                title: null,
                articleTypeId: null, // 设为null，而不是复制当前语言的分类ID
                articleTypePath: null,
                typeCode: 1,
                articleTypeName: null,
                videoUrl: null,
                mainImgUrl: null,
                description: null,
                author: null,
                originFrom: null,
                publicDate: null,
                content: null,
                status: '0',
                orderNum: null,
                createBy: null,
                createTime: null,
                updateTime: null,
                updateBy: null,
                delFlag: null,
                languageType: lang.value,
                remark: null,
                fileUrl: null,
                fileName: null,
                externalLinks: null,
                attachment: null,
                articleFiles: []
              };
              this.multiLanguageForms[lang.value] = template;
            }
          })
        }
        this.$modal.msgSuccess('已清空其他语言版本')
      }).catch(() => { })
    },
    /** 提交按钮 */
    submitForm() {
      // 验证当前语言表单
      this.$refs.form.validate(valid => {
        if (valid) {
          // 校验文件发布日期
          if (this.showFileManagement &&
            this.multiLanguageForms[this.activeLanguageTab].articleFiles &&
            this.multiLanguageForms[this.activeLanguageTab].articleFiles.length > 0) {
            const hasEmptyDate = this.multiLanguageForms[this.activeLanguageTab].articleFiles.some(file => !file.publicDate);
            if (hasEmptyDate) {
              this.$message.error('请为所有附件选择发布日期');
              return;
            }
          }

          // 准备要提交的数据
          const dataToSave = {};

          // 添加有数据的表单
          this.supportedLanguages.forEach(lang => {
            const formData = this.multiLanguageForms[lang.value];
            if (formData && (formData.title || formData.id)) {
              // 处理文件数据
              if (formData.articleFiles) {
                formData.articleFiles = formData.articleFiles.map(file => ({
                  fileName: file.fileName,
                  fileUrl: file.fileUrl,
                  publicDate: file.publicDate,
                  articleId: formData.id || null
                }));
              }

              dataToSave[lang.value] = { ...formData };
            }
          });

          if (Object.keys(dataToSave).length === 0) {
            this.$modal.msgError('请至少填写一个语言版本的数据');
            return;
          }

          this.submitting = true;
          // 批量保存多语言版本
          batchSaveMultiLanguage(dataToSave).then(response => {
            this.$modal.msgSuccess('保存成功');
            this.open = false;
            this.getList();
          }).catch(error => {
            this.$modal.msgError('保存失败：' + (error.msg || error.message));
          }).finally(() => {
            this.submitting = false;
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('注意删除分类，将同步删除此数据关联的其他语言分类，是否确认删除新闻编号为"' + ids + '"的数据项？').then(function () {
        return delArticle(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('yq/article/export', {
        ...this.queryParams
      }, `article_${new Date().getTime()}.xlsx`)
    },
    // 文件上传成功回调
    handleFileChange(files) {
      if (!this.multiLanguageForms[this.activeLanguageTab].articleFiles) {
        this.multiLanguageForms[this.activeLanguageTab].articleFiles = [];
      }

      // 更新文件列表，确保每个文件都有默认日期
      this.multiLanguageForms[this.activeLanguageTab].articleFiles = files.map((file, index) => {
        // 如果文件没有publicDate，设置默认值
        if (!file.publicDate) {
          file.publicDate = this.getCurrentDateTime();
        }
        return {
          fileName: file.fileName,
          fileUrl: file.fileUrl,
          publicDate: file.publicDate,
          orderNum: index
        };
      });
    },

    // 获取当前日期时间
    getCurrentDateTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 删除文件
    handleFileDelete(index) {
      this.multiLanguageForms[this.activeLanguageTab].articleFiles.splice(index, 1);
    },

    // 根据类型显示对应提示文字
    labelDesc(type) {
      let desc = '';
      let typeArr = [10004, 10005, 10006, 10023, 10022, 10021]
      if (this.multiLanguageForms[this.activeLanguageTab] && typeArr.includes(this.multiLanguageForms[this.activeLanguageTab].articleTypeId)) {
        switch (type) {
          case 'title':
            desc = '(姓名)';
            break;
          case 'author':
            desc = '(职位)';
            break;
          case 'originFrom':
            desc = '(召集人)';
            break;
        }
      }
      return desc;
    },
  }
}
</script>

<style scoped>
.file-management {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.label-desc {
  font-size: 12px;
  color: #9b9b9b;
}
</style>
